import AuthService from "@/services/auth.service";
import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { LinearGradient } from "expo-linear-gradient";
import { Link, useRouter } from "expo-router";
import { ArrowRight } from "lucide-react-native";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { I18nManager, StyleSheet, Text, TextInput, TouchableOpacity, View, KeyboardAvoidingView, Platform, ScrollView } from "react-native";
import FitnessLogo from "../../assets/images/fitness-1.svg";
import * as WebBrowser from "expo-web-browser";
import API_CONFIG from "@/config/api.config";
import GOOGLE_AUTH_CONFIG from "@/config/google.auth.config";

export default function LoginScreen() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const router = useRouter();
  const { t } = useTranslation();
  const isRTL = I18nManager.isRTL;

  // Handle Google Sign In
  const handleGoogleSignIn = async () => {
    try {
      setErrorMessage("");

      console.log("Google Sign In pressed");
      const result = await WebBrowser.openAuthSessionAsync(
        `https://accounts.google.com/o/oauth2/v2/auth?` +
          `client_id=${GOOGLE_AUTH_CONFIG.CLIENT_ID}` +
          `&redirect_uri=${encodeURIComponent(API_CONFIG.BASE_URL + "/api/Account/google-callback")}` +
          `&response_type=code` +
          `&scope=${encodeURIComponent("profile email")}`,
        API_CONFIG.BASE_URL
      );

      if (result.type === "success" && result.url) {
        // Extract authorization code from URL
        const url = new URL(result.url);
        const code = url.searchParams.get("code");

        if (code) {
          // Send code to backend for token exchange
          const response = await AuthService.authenticateWithGoogle({ code });

          if (response.accessToken && response.refreshToken) {
            // Decode token or fetch user profile
            const userRole = AuthService.decodeUserRole(response.accessToken);

            // Get user profile based on role
            let user = await (userRole === "club owner" ? AuthService.getClubOwnerProfile() : AuthService.getMemberProfile());

            // Store user session
            const userSession = {
              role: userRole,
              ...user,
            };

            await AsyncStorage.setItem("userSession", JSON.stringify(userSession));

            // Redirect based on user role
            redirectBasedOnRole(userRole);
          }
        }
      }
    } catch (error) {
      console.error("Google Sign In error:", error);
      setErrorMessage("Google Sign In failed. Please try again.");
    }
  };

  useEffect(() => {
    checkExistingSession();
    checkSessionExpired();
  }, []);

  // Check if session expired and show message
  const checkSessionExpired = async () => {
    try {
      const sessionExpired = await AsyncStorage.getItem("session_expired");
      if (sessionExpired === "true") {
        setErrorMessage("Your session has expired. Please sign in again.");
        // Clear the flag after showing the message
        await AsyncStorage.removeItem("session_expired");
      }
    } catch (error) {
      console.error("Error checking session expiration:", error);
    }
  };

  const checkExistingSession = async () => {
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      if (userSession) {
        const userData = JSON.parse(userSession);
        console.log("Existing session:", userData);

        // Check if we have all required session data
        if (userData && userData.role) {
          // Verify if the session is still valid by checking tokens
          const accessToken = await AsyncStorage.getItem("auth_token");
          const refreshToken = await AsyncStorage.getItem("refresh_token");

          if (accessToken && refreshToken) {
            // Session is valid, redirect based on role
            redirectBasedOnRole(userData.role);
          }
        }
      }
    } catch (error) {
      console.error("Error checking session:", error);
      setErrorMessage("Error checking session. Please try again.");
    }
  };

  const redirectBasedOnRole = (role: string) => {
    switch (role) {
      case "club owner":
        router.replace("/(tabs)/owner/home");
        break;
      case "user":
        router.replace("/(tabs)/user/clubs");
        break;
      default:
        router.replace("/(tabs)/user/clubs"); // Default fallback
    }
  };

  const handleLogin = async () => {
    try {
      // Clear any previous error messages
      setErrorMessage("");

      if (!email || !password) {
        setErrorMessage("Please enter both email and password");
        return;
      }

      // Call AuthService login method
      const response = await AuthService.login({ username: email, password });

      // Check if the response contains tokens
      if (response.accessToken && response.refreshToken) {
        // Decode token or fetch user profile
        const userRole = AuthService.decodeUserRole(response.accessToken);

        let user: any = {
          role: userRole,
          username: email,
          email: email,
          phone: "",
          firstname: "",
          lastname: "",
          address: "",
          gender: "",
        };

        if (userRole === "club owner") {
          user = await AuthService.getClubOwnerProfile();
        } else if (userRole === "user") {
          user = await AuthService.getMemberProfile();
        }

        // Store user session
        const userSession = {
          role: userRole,
          ...user,
        };

        await AsyncStorage.setItem("userSession", JSON.stringify(userSession));

        if (rememberMe) {
          // Store credentials if remember me is checked
          await AsyncStorage.setItem(
            "rememberedUser",
            JSON.stringify({
              username: email,
              rememberMe: true,
            })
          );
        } else {
          // Clear remembered user if remember me is unchecked
          await AsyncStorage.removeItem("rememberedUser");
        }

        // Redirect based on user role
        redirectBasedOnRole(userRole);
      } else {
        setErrorMessage("Invalid credentials.");
      }
    } catch (error: any) {
      console.error("Error during login:", error);
      setErrorMessage("Invalid credentials. Please try again.");
    }
  };

  return (
    <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : "height"} style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer} keyboardShouldPersistTaps="handled">
        <LinearGradient colors={["#4A69FF", "#4A69FF", "#7C8FFF"]} locations={[0, 0.5, 1]} style={styles.gradientBackground as any}>
          {/* Footer link at top */}
          <View style={[styles.footer, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
            <Link href="/(tabs)/user/clubs" asChild>
              <TouchableOpacity style={{ flexDirection: isRTL ? "row-reverse" : "row" }}>
                <Text style={styles.link}>{t("skipLogin")}</Text>
                <ArrowRight
                  size={16}
                  color="#fff"
                  style={{
                    marginLeft: isRTL ? 0 : 2,
                    marginRight: isRTL ? 2 : 0,
                  }}
                />
              </TouchableOpacity>
            </Link>
          </View>

          {/* Main content */}
          <View style={styles.contentContainer}>
            <FitnessLogo width={"100%"} height={280} style={styles.logo} />
            <Text style={[styles.subtitle, { textAlign: isRTL ? "right" : "left" }]}>{t("signInToContinue")}</Text>

            {/* Form inputs */}
            <View style={styles.inputContainer}>
              <TextInput
                style={[
                  styles.input,
                  {
                    textAlign: isRTL ? "right" : "left",
                    writingDirection: isRTL ? "rtl" : "ltr",
                  },
                ]}
                placeholder={t("email")}
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                returnKeyType="next"
              />
              <TextInput
                style={[
                  styles.input,
                  {
                    textAlign: isRTL ? "right" : "left",
                    writingDirection: isRTL ? "rtl" : "ltr",
                  },
                ]}
                placeholder={t("password")}
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                returnKeyType="done"
              />
              {errorMessage && <Text style={styles.errorText}>{t(errorMessage)}</Text>}
            </View>

            {/* Remember me & forgot password */}
            <View style={[styles.rememberMeContainer, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
              <TouchableOpacity
                style={[styles.checkboxContainer, { flexDirection: isRTL ? "row-reverse" : "row" }]}
                onPress={() => setRememberMe(!rememberMe)}
              >
                <View style={[styles.checkbox, { marginRight: isRTL ? 0 : 8, marginLeft: isRTL ? 8 : 0 }]}>
                  {rememberMe && <Ionicons name="checkmark" size={16} color="#ffffff" />}
                </View>
                <Text style={[styles.checkboxText, { textAlign: isRTL ? "right" : "left" }]}>{t("rememberMe")}</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => router.push("https://www.gogymium.com/Account/forgot-password")}>
                <Text style={[styles.forgotPassword, { textAlign: isRTL ? "right" : "left" }]}>{t("forgotPassword")}</Text>
              </TouchableOpacity>
            </View>

            {/* Login button */}
            <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
              <Text style={styles.loginButtonText}>{t("signIn")}</Text>
            </TouchableOpacity>

            {/* Divider */}
            <View style={styles.dividerContainer}>
              <View style={styles.divider} />
              <Text style={styles.dividerText}>{t("or")}</Text>
              <View style={styles.divider} />
            </View>

            {/* Google Sign In button */}
            <TouchableOpacity style={[styles.googleButton, { flexDirection: isRTL ? "row-reverse" : "row" }]} onPress={handleGoogleSignIn}>
              <Ionicons
                name="logo-google"
                size={20}
                color="#4A69FF"
                style={[styles.googleIcon, { marginRight: isRTL ? 0 : 10, marginLeft: isRTL ? 10 : 0 }]}
              />
              <Text style={styles.googleButtonText}>{t("signInWithGoogle")}</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#4A69FF",
  },
  scrollContainer: {
    flexGrow: 1,
  },
  gradientBackground: {
    flex: 1,
    padding: 20,
    justifyContent: "center",
    paddingTop: 50,
  },
  contentContainer: {
    width: "100%",
    maxWidth: 400,
    alignSelf: "center",
  },
  logo: {
    width: "100%",
    height: 280,
    marginBottom: 20,
    alignSelf: "center",
  },
  subtitle: {
    fontSize: 16,
    color: "#ffffff",
    marginBottom: 20,
    opacity: 0.8,
  },
  inputContainer: {
    gap: 15,
    marginBottom: 20,
  },
  input: {
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    borderRadius: 12,
    padding: 15,
    fontSize: 16,
    color: "#ffffff",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  rememberMeContainer: {
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  checkboxContainer: {
    alignItems: "center",
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: "#ffffff",
    justifyContent: "center",
    alignItems: "center",
  },
  checkboxText: {
    color: "#ffffff",
    fontSize: 14,
  },
  forgotPassword: {
    color: "#ffffff",
    fontSize: 14,
  },
  loginButton: {
    backgroundColor: "#ffffff",
    padding: 15,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 20,
  },
  loginButtonText: {
    color: "#4A69FF",
    fontSize: 16,
    fontWeight: "600",
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
  },
  dividerText: {
    color: "#ffffff",
    paddingHorizontal: 10,
    fontSize: 14,
    opacity: 0.8,
  },
  googleButton: {
    backgroundColor: "#ffffff",
    padding: 15,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 10,
  },
  googleIcon: {
    // Margin is handled inline with RTL support
  },
  googleButtonText: {
    color: "#4A69FF",
    fontSize: 16,
    fontWeight: "600",
  },
  footer: {
    justifyContent: "flex-end",
    marginBottom: 16,
  },
  link: {
    color: "#ffffff",
    fontWeight: "600",
    marginLeft: 5,
    textDecorationLine: "underline",
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 14,
    marginTop: 8,
    marginBottom: 8,
    textAlign: "center",
  },
});
