{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slug": "gogymium", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "We need access to your camera to take a photo", "NSPhotoLibraryUsageDescription": "We need access to your photo library to select an image", "NSPhotoLibraryAddUsageDescription": "We need access to save images", "ITSAppUsesNonExemptEncryption": false}, "bundleIdentifier": "com.morad.elm9.gogymium"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"], "package": "com.morad.elm9.gogymium"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"origin": false, "asyncRoutes": false}], "expo-localization", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-image-picker", ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], ["@react-native-google-signin/google-signin", {"iosUrlScheme": "595684359316-jvis38u6vuej06bustfgs1u82s3ufeit.apps.googleusercontent.com"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "6a067f4b-416c-497f-8125-461eb49c16b7"}}, "owner": "morad.elm9"}}