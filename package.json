{"name": "gogymuim-mobile-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start -c --port 7076", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/cli-server-api": "^15.1.3", "@react-native-google-signin/google-signin": "^14.0.1", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.0.0", "@react-navigation/native": "^7.0.0", "date-fns": "^4.1.0", "expo": "^52.0.0", "expo-barcode-scanner": "^13.0.1", "expo-blur": "~14.0.3", "expo-camera": "~16.0.18", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.20", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-localization": "~16.0.1", "expo-router": "~4.0.21", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.2", "i18next": "^24.1.0", "jwt-decode": "^4.0.0", "lucide-react-native": "^0.462.0", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.1.4", "react-native": "0.76.9", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.20.2", "react-native-image-picker": "^8.0.0", "react-native-localize": "^3.3.0", "react-native-permissions": "^5.2.6", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-snap-carousel": "^3.9.1", "react-native-svg": "15.8.0", "react-native-svg-image": "^2.0.1", "react-native-svg-transformer": "^1.5.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "~29.7.0", "jest-expo": "~52.0.6", "react-test-renderer": "18.3.1", "typescript": "~5.8.3"}, "private": true}